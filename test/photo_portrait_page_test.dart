import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/view/photo_restoration/photo_portrait_page.dart';

void main() {
  group('PhotoPortraitPage Tests', () {
    testWidgets('PhotoPortraitPage should build without errors', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const PhotoPortraitPage(),
          ),
        ),
      );

      // Verify that the page title is displayed
      expect(find.text('真人写真'), findsOneWidget);
      
      // Verify that the "生成记录" button is displayed
      expect(find.text('生成记录'), findsOneWidget);
      
      // Verify that category titles are displayed
      expect(find.text('热门'), findsOneWidget);
      expect(find.text('双重曝光'), findsOneWidget);
      expect(find.text('网感大片'), findsOneWidget);
      expect(find.text('宠物写真'), findsOneWidget);
      
      // Verify that "更多" buttons are displayed
      expect(find.text('更多'), findsWidgets);
      
      // Verify that ID photo sections are displayed
      expect(find.text('证件照'), findsWidgets);
      expect(find.text('ID PHOTO'), findsWidgets);
    });

    testWidgets('PhotoPortraitPage should be scrollable', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const PhotoPortraitPage(),
          ),
        ),
      );

      // Find the SingleChildScrollView
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      
      // Verify that we can scroll
      await tester.drag(find.byType(SingleChildScrollView), const Offset(0, -300));
      await tester.pump();
    });

    testWidgets('PhotoPortraitPage should have proper layout structure', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const PhotoPortraitPage(),
          ),
        ),
      );

      // Verify AppBar exists
      expect(find.byType(AppBar), findsOneWidget);
      
      // Verify body structure
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      expect(find.byType(Column), findsWidgets);
      
      // Verify horizontal ListView for ID photos
      expect(find.byType(ListView), findsWidgets);
    });
  });
}
