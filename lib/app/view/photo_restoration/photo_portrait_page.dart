import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';

class PhotoPortraitPage extends ConsumerWidget {
  const PhotoPortraitPage({super.key});

  // 模拟数据 - 证件照类型
  final List<Map<String, String>> idPhotoTypes = const [
    {"title": "证件照", "subtitle": "ID PHOTO"},
    {"title": "证件照", "subtitle": "ID PHOTO"},
    {"title": "证件照", "subtitle": "ID PHOTO"},
    {"title": "证件照", "subtitle": "ID PHOTO"},
    {"title": "证件照", "subtitle": "ID PHOTO"},
  ];

  // 模拟数据 - 写真分类
  final List<Map<String, dynamic>> portraitCategories = const [
    {
      "title": "热门",
      "images": [
        "https://picsum.photos/200/300?random=1",
        "https://picsum.photos/200/350?random=2",
        "https://picsum.photos/200/280?random=3",
        "https://picsum.photos/200/320?random=4",
      ]
    },
    {
      "title": "双重曝光",
      "images": [
        "https://picsum.photos/200/290?random=5",
        "https://picsum.photos/200/310?random=6",
        "https://picsum.photos/200/270?random=7",
        "https://picsum.photos/200/340?random=8",
      ]
    },
    {
      "title": "网感大片",
      "images": [
        "https://picsum.photos/200/300?random=9",
        "https://picsum.photos/200/280?random=10",
        "https://picsum.photos/200/320?random=11",
        "https://picsum.photos/200/290?random=12",
      ]
    },
    {
      "title": "宠物写真",
      "images": [
        "https://picsum.photos/200/310?random=13",
        "https://picsum.photos/200/270?random=14",
        "https://picsum.photos/200/330?random=15",
        "https://picsum.photos/200/300?random=16",
      ]
    },
  ];

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: SizedBox(
              height: 80,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                itemCount: idPhotoTypes.length,
                separatorBuilder: (context, index) => const SizedBox(width: 12),
                itemBuilder: (context, index) {
                  final item = idPhotoTypes[index];
                  return Container(
                    width: 120,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Color(0xFF4A90E2),
                          Color(0xFF357ABD),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Stack(
                      children: [
                        // 背景装饰
                        Positioned(
                          top: -10,
                          right: -10,
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                        // 文字内容
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                item["title"]!,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                item["subtitle"]!,
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.white.withValues(alpha: 0.8),
                                  letterSpacing: 1.2,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySection(Map<String, dynamic> category) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                category["title"],
                style: const TextStyle(
                  fontSize: 18,
                  color: Colors.white,
                ),
              ),
              const Row(
                children: [
                  Text(
                    "更多",
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(width: 4),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 8,
                    color: Colors.white,
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 160,
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            scrollDirection: Axis.horizontal,
            itemCount: category["images"].length,
            separatorBuilder: (context, index) => const SizedBox(width: 8),
            itemBuilder: (context, index) {
              return Container(
                width: 110,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      // 使用占位图片，实际项目中替换为网络图片
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.purple.withValues(alpha: 0.6),
                              Colors.pink.withValues(alpha: 0.8),
                              Colors.orange.withValues(alpha: 0.6),
                            ],
                          ),
                        ),
                      ),
                      // 渐变遮罩
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withValues(alpha: 0.3),
                            ],
                          ),
                        ),
                      ),
                      // 点击效果
                      Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            // 处理点击事件
                          },
                          child: Container(),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        title: const Text(
          "真人写真",
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          InkWell(
            onTap: () {},
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
              decoration: BoxDecoration(
                color: const Color(0x30FFFFFF),
                borderRadius: BorderRadius.circular(13),
              ),
              child: const Text(
                "生成记录",
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 21),
            _buildHeader(),
            const SizedBox(height: 32),
            ...portraitCategories.map((category) => Column(
                  children: [
                    _buildCategorySection(category),
                    const SizedBox(height: 32),
                  ],
                )),
            SizedBox(height: MediaQuery.of(context).padding.bottom + 20),
          ],
        ),
      ),
    );
  }
}
