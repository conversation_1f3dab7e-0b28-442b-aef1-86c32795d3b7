import 'package:json_annotation/json_annotation.dart';

part 'photo_portrait.g.dart';

@JsonSerializable()
class PhotoPortraitCategory {
  String? caseName;
  String? createTime;
  List<PhotoPortraitCategoryDetail>? details;
  int? id;
  int? sort;
  int? state;
  String? updateTime;

  PhotoPortraitCategory();

  factory PhotoPortraitCategory.fromJson(Map<String, dynamic> json) =>
      _$PhotoPortraitCategoryFromJson(json);

  Map<String, dynamic> toJson() => _$PhotoPortraitCategoryToJson(this);
}

@JsonSerializable()
class PhotoPortraitCategoryDetail {
  int? caseHeight;
  int? caseId;
  String? caseImage;
  String? casePrompt;
  String? caseTitle;
  int? caseWidth;
  String? createTime;
  int? id;
  int? sort;
  int? state;
  String? updateTime;

  PhotoPortraitCategoryDetail();

  factory PhotoPortraitCategoryDetail.fromJson(Map<String, dynamic> json) =>
      _$PhotoPortraitCategoryDetailFromJson(json);

  Map<String, dynamic> toJson() => _$PhotoPortraitCategoryDetailToJson(this);
}

@JsonSerializable()
class PhotoPortraitBanner {
  String? caseImage;
  int? jumpId;
  int? sort;

  PhotoPortraitBanner({
    this.caseImage,
    this.jumpId,
    this.sort,
  });
  factory PhotoPortraitBanner.fromJson(Map<String, dynamic> json) =>
      _$PhotoPortraitBannerFromJson(json);

  Map<String, dynamic> toJson() => _$PhotoPortraitBannerToJson(this);
}