// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_portrait.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PhotoPortraitCategory _$PhotoPortraitCategoryFromJson(
        Map<String, dynamic> json) =>
    PhotoPortraitCategory()
      ..caseName = json['caseName'] as String?
      ..createTime = json['createTime'] as String?
      ..details = (json['details'] as List<dynamic>?)
          ?.map((e) =>
              PhotoPortraitCategoryDetail.fromJson(e as Map<String, dynamic>))
          .toList()
      ..id = (json['id'] as num?)?.toInt()
      ..sort = (json['sort'] as num?)?.toInt()
      ..state = (json['state'] as num?)?.toInt()
      ..updateTime = json['updateTime'] as String?;

Map<String, dynamic> _$PhotoPortraitCategoryToJson(
        PhotoPortraitCategory instance) =>
    <String, dynamic>{
      'caseName': instance.caseName,
      'createTime': instance.createTime,
      'details': instance.details,
      'id': instance.id,
      'sort': instance.sort,
      'state': instance.state,
      'updateTime': instance.updateTime,
    };

PhotoPortraitCategoryDetail _$PhotoPortraitCategoryDetailFromJson(
        Map<String, dynamic> json) =>
    PhotoPortraitCategoryDetail()
      ..caseHeight = (json['caseHeight'] as num?)?.toInt()
      ..caseId = (json['caseId'] as num?)?.toInt()
      ..caseImage = json['caseImage'] as String?
      ..casePrompt = json['casePrompt'] as String?
      ..caseTitle = json['caseTitle'] as String?
      ..caseWidth = (json['caseWidth'] as num?)?.toInt()
      ..createTime = json['createTime'] as String?
      ..id = (json['id'] as num?)?.toInt()
      ..sort = (json['sort'] as num?)?.toInt()
      ..state = (json['state'] as num?)?.toInt()
      ..updateTime = json['updateTime'] as String?;

Map<String, dynamic> _$PhotoPortraitCategoryDetailToJson(
        PhotoPortraitCategoryDetail instance) =>
    <String, dynamic>{
      'caseHeight': instance.caseHeight,
      'caseId': instance.caseId,
      'caseImage': instance.caseImage,
      'casePrompt': instance.casePrompt,
      'caseTitle': instance.caseTitle,
      'caseWidth': instance.caseWidth,
      'createTime': instance.createTime,
      'id': instance.id,
      'sort': instance.sort,
      'state': instance.state,
      'updateTime': instance.updateTime,
    };

PhotoPortraitBanner _$PhotoPortraitBannerFromJson(Map<String, dynamic> json) =>
    PhotoPortraitBanner(
      caseImage: json['caseImage'] as String?,
      jumpId: (json['jumpId'] as num?)?.toInt(),
      sort: (json['sort'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PhotoPortraitBannerToJson(
        PhotoPortraitBanner instance) =>
    <String, dynamic>{
      'caseImage': instance.caseImage,
      'jumpId': instance.jumpId,
      'sort': instance.sort,
    };
