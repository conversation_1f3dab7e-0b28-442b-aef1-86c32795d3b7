import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:text_generation_video/app/provider/audio_img_to_video/audio_case_provider.dart';
import 'package:text_generation_video/app/repository/modals/audio_img_to_video/audio_img_to_video.dart';
import 'package:text_generation_video/app/view/audio_img_to_video/dialog/audio_img_to_video_dialog.dart';
import 'package:text_generation_video/app/view/audio_img_to_video/dialog/upload_photo_dialog.dart';
import 'package:text_generation_video/app/widgets/image_upload/upload_status_widget.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/widgets/consumption/consumption_display_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';

class AudioCaseImgToVideoWidget extends ConsumerStatefulWidget {
  const AudioCaseImgToVideoWidget({
    super.key,
    this.templateTitle = "歌曲模版",
    this.pageType = PageType.petSinging,
  });
  final PageType pageType;
  final String templateTitle;
  
  @override
  ConsumerState<AudioCaseImgToVideoWidget> createState() => _AudioCaseImgToVideoWidgetState();
}



class _AudioCaseImgToVideoWidgetState extends ConsumerState<AudioCaseImgToVideoWidget> {
  Widget _buildCaseList(List<AudioImgToVideo> caseList) {
    var itemWidth = 85.81.w;
    var itemHeight = 115 * itemWidth / 85.81;
    // 获取当前选中的案例
    final selectedCase = ref.watch(audioCaseToVideoCaseProvider);

    return SizedBox(
      height: itemHeight,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, index) {
          var caseItem = caseList[index];
          // 判断当前案例是否被选中
          final isSelected = selectedCase != null &&
              selectedCase.caseName == caseItem.caseName &&
              selectedCase.videoUrl == caseItem.videoUrl;

          return GestureDetector(
            onTap: () {
              // 处理案例点击事件
              _onCaseItemTap(caseItem);
            },
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: isSelected ? const Color(0xFF30E6B8) : null,
                  ),
                  child: Container(
                    margin: const EdgeInsets.all(2),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(isSelected ? 10 : 12),
                      child: CachedNetworkImage(
                        imageUrl: caseItem.cover,
                        width: itemWidth,
                        height: itemHeight,
                        fit: BoxFit.cover,
                        errorWidget: (_, o, s) {
                          return Container(
                            width: itemWidth,
                            height: itemHeight,
                            color: Colors.grey.shade800,
                          );
                        },
                      ),
                    ),
                  ),
                ),
                Container(
                  width: itemWidth,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                  margin: const EdgeInsets.only(bottom: 2),
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.7),
                      ],
                    ),
                  ),
                  child: Text(
                    caseItem.caseName,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 11,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
        separatorBuilder: (context, index) {
          return const SizedBox(width: 10);
        },
        itemCount: caseList.length,
      ),
    );
  }

  // 处理案例点击事件
  void _onCaseItemTap(AudioImgToVideo caseItem) {
    // 这里可以添加点击案例后的逻辑，比如播放视频、选择模板等
    debugPrint("选择了案例: ${caseItem.caseName}");
    // 弹出视频播放弹窗
    AudioImgToVideoDialog.showAudioImgToVideoDialog(context, caseItem);
  }

  // 歌曲模版标题部分
  Widget _buildCaseTitle() {
    return Container(
      margin: const EdgeInsets.fromLTRB(14, 0, 14, 13),
      child: Row(
        children: [
          Text(
            widget.templateTitle,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFFFFFFFF),
            ),
          ),
          const SizedBox(width: 10),
          Container(
            width: 1,
            height: 12,
            color: const Color(0xFFD8D8D8),
          ),
          const SizedBox(width: 10),
          Text(
            ref.read(audioCaseToVideoCaseProvider)?.caseName ?? "点击选择模版",
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF8A8D93),
            ),
          ),
        ],
      ),
    );
  }

  // 构建完整的案例模块，包括标题和列表
  Widget _buildCase() {
    final caseListAsync = ref.watch(fetchLipSyncCaseProvider(widget.pageType));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCaseTitle(),
        caseListAsync.when(
          data: (caseList) {
            if (caseList == null || caseList.isEmpty) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: Text(
                    "暂无模版数据",
                    style: TextStyle(color: Colors.white60),
                  ),
                ),
              );
            }
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 14),
              child: _buildCaseList(caseList),
            );
          },
          loading: () => const Center(
            child: Padding(
              padding: EdgeInsets.all(20.0),
              child: CircularProgressIndicator(),
            ),
          ),
          error: (error, stackTrace) => Center(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Text(
                "加载失败: $error",
                style: const TextStyle(color: Colors.red),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUploadImg() {
    final currentModification = ref.watch(photoModificationCurrentProvider);
    return Container(
      margin: const EdgeInsets.fromLTRB(14, 14, 14, 0),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2C2F),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(color: const Color(0xFF565656), width: 0.6),
      ),
      width: double.infinity,
      child: UploadStatusWidget(
        modification: currentModification,
        defaultChild: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              builder: (context) {
                return UploadPhotoDialogWidget(
                  pageType: widget.pageType,
                  onTakePhoto: () {
                    ref
                        .read(photoModificationCurrentProvider.notifier)
                        .selectImg(source: ImageSource.camera);
                  },
                  onSelectPhoto: () {
                    ref
                        .read(photoModificationCurrentProvider.notifier)
                        .selectImg();
                  },
                );
              },
            );
          },
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(modificationSelectIcon, width: 26),
              const SizedBox(height: 9),
              const Text(
                "上传照片",
                style: TextStyle(fontSize: 14, color: Color(0xFF8A8D93)),
              ),
              const SizedBox(height: 4),
              const Text(
                "请根据上传的图片类型\n选择对应的任务或宠物模板",
                style: TextStyle(fontSize: 12, color: Color(0xFF8A8D93)),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      _buildCase(),
      Expanded(child: _buildUploadImg()),
      const SizedBox(height: 12),
      const Padding(
        padding: EdgeInsets.symmetric(horizontal: 16),
        child: ConsumptionDisplayWidget(consumption: 20),
      ),
      GradientButton(
        onPress: () {
          ref.read(audioCaseToVideoCaseProvider.notifier).commit(widget.pageType);
        },
        // enable: false,
        margin: const EdgeInsets.symmetric(horizontal: 16),
        padding: const EdgeInsets.symmetric(vertical: 15),
        radius: 16,
        shadow: false,
        gradient: const LinearGradient(
          colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
        ),
        child: const Text(
          "开始改图",
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF18161A),
          ),
        ),
      ),
      SizedBox(height: MediaQuery.paddingOf(context).bottom + 20),
    ]);
  }
}
